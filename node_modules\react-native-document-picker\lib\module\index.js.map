{"version": 3, "names": ["Platform", "invariant", "perPlatformTypes", "NativeDocumentPicker", "types", "OS", "pickDirectory", "params", "result", "pick", "mode", "allowMultiSelection", "type", "uri", "pickSingle", "opts", "options", "then", "results", "allFiles", "newOpts", "presentationStyle", "transitionStyle", "Array", "isArray", "doPick", "every", "length", "includes", "TypeError", "copyTo", "releaseSecureAccess", "uris", "Promise", "resolve", "E_DOCUMENT_PICKER_CANCELED", "E_DOCUMENT_PICKER_IN_PROGRESS", "isCancel", "err", "isErrorWithCode", "isInProgress", "errorCode", "nativeModuleErrorInstance", "code"], "sourceRoot": "../../src", "sources": ["index.tsx"], "mappings": "AAAA,SAASA,QAAQ,QAAuB,cAAc;AACtD,OAAOC,SAAS,MAAM,WAAW;AAEjC,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,SAASC,oBAAoB,QAAQ,wBAAwB;AAW7D,OAAO,MAAMC,KAAK,GAAGF,gBAAgB,CAACF,QAAQ,CAACK,EAAE,CAAC;AAgBlD,OAAO,eAAeC,aAAaA,CACjCC,MAA6E,EACpC;EACzC,IAAIP,QAAQ,CAACK,EAAE,KAAK,KAAK,EAAE;IACzB,MAAMG,MAAM,GAAG,MAAMC,IAAI,CAAC;MACxB,GAAGF,MAAM;MACTG,IAAI,EAAE,MAAM;MACZC,mBAAmB,EAAE,KAAK;MAC1BC,IAAI,EAAE,CAAC,eAAe;IACxB,CAAC,CAAC;IACF,OAAO;MAAEC,GAAG,EAAEL,MAAM,CAAC,CAAC,CAAC,CAACK;IAAI,CAAC;EAC/B,CAAC,MAAM;IACL,OAAOV,oBAAoB,CAACG,aAAa,CAAC,CAAC;EAC7C;AACF;AAEA,OAAO,SAASQ,UAAUA,CAACC,IAA4B,EAAmC;EACxF,MAAMC,OAAO,GAAG;IACd,GAAGD,IAAI;IACPJ,mBAAmB,EAAE;EACvB,CAAC;EACD,OAAOF,IAAI,CAACO,OAAO,CAAC,CAACC,IAAI,CAAEC,OAAO,IAAKA,OAAO,CAAC,CAAC,CAAC,CAAC;AACpD;AAEA,OAAO,SAAST,IAAIA,CAACM,IAA4B,EAAqC;EACpF,MAAMC,OAAO,GAAG;IACd;IACAL,mBAAmB,EAAE,KAAK;IAC1BC,IAAI,EAAE,CAACR,KAAK,CAACe,QAAQ,CAAC;IACtB,GAAGJ;EACL,CAAC;EAED,MAAMK,OAAqB,GAAG;IAC5BC,iBAAiB,EAAE,WAAW;IAC9BC,eAAe,EAAE,eAAe;IAChC,GAAGN,OAAO;IACVJ,IAAI,EAAEW,KAAK,CAACC,OAAO,CAACR,OAAO,CAACJ,IAAI,CAAC,GAAGI,OAAO,CAACJ,IAAI,GAAG,CAACI,OAAO,CAACJ,IAAI;EAClE,CAAC;EAED,OAAOa,MAAM,CAACL,OAAO,CAAC;AACxB;AASA,SAASK,MAAMA,CAACT,OAAqB,EAAqC;EACxEf,SAAS,CACP,EAAE,UAAU,IAAIe,OAAO,CAAC,EACxB,qFACF,CAAC;EACDf,SAAS,CACP,EAAE,OAAO,IAAIe,OAAO,CAAC,EACrB,kFACF,CAAC;EAEDf,SAAS,CACPe,OAAO,CAACJ,IAAI,CAACc,KAAK,CAAEd,IAAa,IAAK,OAAOA,IAAI,KAAK,QAAQ,CAAC,EAC9D,6BAA4BI,OAAO,CAACJ,IAAK,mEAC5C,CAAC;EACDX,SAAS,CACPe,OAAO,CAACJ,IAAI,CAACe,MAAM,GAAG,CAAC,EACvB,kHACF,CAAC;EAED1B,SAAS,CACP,CAACe,OAAO,CAACJ,IAAI,CAACgB,QAAQ,CAAC,QAAQ,CAAC,EAChC,wEACF,CAAC;EAED,IAAI,MAAM,IAAIZ,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAACY,QAAQ,CAACZ,OAAO,CAACN,IAAI,IAAI,EAAE,CAAC,EAAE;IACzE,MAAM,IAAImB,SAAS,CAAC,uBAAuB,GAAGb,OAAO,CAACN,IAAI,CAAC;EAC7D;EAEA,IACE,QAAQ,IAAIM,OAAO,IACnB,CAAC,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAACY,QAAQ,CAACZ,OAAO,CAACc,MAAM,IAAI,EAAE,CAAC,EACxE;IACA,MAAM,IAAID,SAAS,CAAC,yBAAyB,GAAGb,OAAO,CAACc,MAAM,CAAC;EACjE;EAEA,OAAO3B,oBAAoB,CAACM,IAAI,CAACO,OAAO,CAAC;AAC3C;AAEA,OAAO,SAASe,mBAAmBA,CAACC,IAAmB,EAAiB;EACtE,IAAIhC,QAAQ,CAACK,EAAE,KAAK,KAAK,EAAE;IACzB,OAAO4B,OAAO,CAACC,OAAO,CAAC,CAAC;EAC1B;EAEAjC,SAAS,CACPsB,KAAK,CAACC,OAAO,CAACQ,IAAI,CAAC,IAAIA,IAAI,CAACN,KAAK,CAAEb,GAAG,IAAK,OAAOA,GAAG,KAAK,QAAQ,CAAC,EAClE,6CAA4CmB,IAAK,EACpD,CAAC;EAED,OAAO7B,oBAAoB,CAAC4B,mBAAmB,CAACC,IAAI,CAAC;AACvD;AAEA,MAAMG,0BAA0B,GAAG,0BAA0B;AAC7D,MAAMC,6BAA6B,GAAG,sBAAsB;AAI5D,OAAO,SAASC,QAAQA,CAACC,GAAY,EAAW;EAC9C,OAAOC,eAAe,CAACD,GAAG,EAAEH,0BAA0B,CAAC;AACzD;AAEA,OAAO,SAASK,YAAYA,CAACF,GAAY,EAAW;EAClD,OAAOC,eAAe,CAACD,GAAG,EAAEF,6BAA6B,CAAC;AAC5D;AAEA,SAASG,eAAeA,CAACD,GAAY,EAAEG,SAAiB,EAAW;EACjE,IAAIH,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,MAAM,IAAIA,GAAG,EAAE;IACnD,MAAMI,yBAAyB,GAAGJ,GAA6B;IAC/D,OAAO,CAAAI,yBAAyB,aAAzBA,yBAAyB,uBAAzBA,yBAAyB,CAAEC,IAAI,MAAKF,SAAS;EACtD;EACA,OAAO,KAAK;AACd;AAEA,eAAe;EACbJ,QAAQ;EACRG,YAAY;EACZT,mBAAmB;EACnBzB,aAAa;EACbG,IAAI;EACJK,UAAU;EACVV,KAAK;EACLF;AACF,CAAC"}