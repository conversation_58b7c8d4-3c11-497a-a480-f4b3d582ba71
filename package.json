{"name": "logistics-service-provider-app", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject"}, "dependencies": {"@ant-design/react-native": "^5.4.2", "@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "^2.2.0", "@types/react": "~19.0.10", "expo": "~53.0.0", "expo-dev-client": "~5.2.4", "expo-font": "~13.3.2", "expo-splash-screen": "^0.30.10", "expo-status-bar": "~2.2.3", "pocketbase": "^0.26.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-native": "^0.79.5", "react-native-document-picker": "^9.3.1", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.28.0", "typescript": "~5.8.3"}, "private": true}