{"name": "@ant-design/react-native", "version": "5.4.2", "description": "基于蚂蚁金服移动设计规范的 React Native 组件库", "keywords": ["ant", "design", "react", "react-component", "component", "components", "ui", "framework", "frontend", "mobile", "react native"], "repository": {"type": "git", "url": "http://github.com/ant-design/ant-design-mobile-rn"}, "bugs": {"url": "http://github.com/ant-design/ant-design-mobile-rn/issues"}, "files": ["lib", "es"], "license": "MIT", "main": "lib/index.js", "module": "es/index.js", "dependencies": {"@bang88/react-native-ultimate-listview": "^4.1.1", "@floating-ui/react-native": "^0.10.6", "@rc-component/mini-decimal": "^1.1.0", "babel-runtime": "^6.x", "classnames": "^2.2.1", "dayjs": "^1.11.7", "lodash.assignwith": "^4.2.0", "lodash.mergewith": "^4.6.2", "normalize-css-color": "^1.0.2", "rc-field-form": "^2.0.0", "rc-util": "^5.39.1", "react-native-codegen": "^0.0.7", "react-native-collapsible": "^1.6.0", "react-native-modal-popover": "^2.0.1", "utility-types": "^3.10.0"}, "devDependencies": {"@ant-design/icons-react-native": "2.3.2", "@ant-design/tools": "^17.3.0", "@babel/core": "^7.12.9", "@babel/runtime": "^7.12.5", "@react-native-community/eslint-config": "^2.0.0", "@react-navigation/native": "^6.1.1", "@react-navigation/stack": "^6.3.10", "@testing-library/jest-native": "^4.0.1", "@testing-library/react-native": "^7.2.0", "@types/jest": "^26.0.23", "@types/lodash.mergewith": "^4.6.9", "@types/prop-types": "^15.7.3", "@types/react": "^17.0.85", "@types/react-native": "^0.67.0", "@typescript-eslint/parser": "^4.26.1", "ant-design-palettes": "^1.1.3", "antd": "3.11.1", "antd-mobile-demo-data": "^0.2.0", "babel-eslint": "^10.1.0", "babel-jest": "^26.6.3", "bisheng": "^3.1.0-beta.4", "concurrently": "^4.0.1", "cross-env": "^7.0.3", "enquire.js": "^2.1.1", "eslint": "^7.28.0", "glob": "^7.1.1", "jest": "^26.6.3", "jsonml.js": "^0.1.0", "lint-staged": "^8.0.4", "metro-react-native-babel-preset": "^0.66.0", "mockdate": "^2.0.1", "pre-commit": "1.x", "react": "17.0.2", "react-copy-to-clipboard": "^5.0.0", "react-document-title": "^2.0.1", "react-dom": "17.0.2", "react-github-button": "^0.1.9", "react-intl": "^2.2.3", "react-native": "0.67.5", "react-native-gesture-handler": "~2.14.0", "react-native-mocker": "^0.0.12", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "4.2.4", "react-native-screens": "^3.14.1", "react-native-web": "^0.17.6", "react-test-renderer": "17.0.2", "shell-utils": "^1.0.10", "ts-jest": "26.5.6", "tslint": "^6.1.3", "typescript": "~5.6.2"}, "peerDependencies": {"@ant-design/icons-react-native": ">= 2.3.2", "react": ">=17.0.1", "react-native": ">=0.67.5", "react-native-gesture-handler": ">=2.14.0", "react-native-reanimated": ">=3.10.1"}, "scripts": {"lint": "npm run tslint && npm run srclint && npm run applint", "srclint": "eslint site scripts --ext '.js,.jsx' --fix", "tslint": "eslint components --fix && npm run compile && rm -rf lib", "applint": "eslint rn-kitchen-sink --ext '.js' --fix", "pre-publish": "npm run test:all", "prepublish": "antd-tools run guard", "prepare": "antd-tools run guard", "prepublishOnly": "antd-tools run guard", "test": "jest --config .jest.config.js", "test:all": "./scripts/test-all.sh", "compile": "cross-env BABEL_ENV=dist antd-tools run compile", "watch-tsc": "tsc --watch", "clean": "antd-tools run clean", "start": "cross-env NODE_ENV=development cross-env DEMO_ENV=react bisheng start -c ./site/bisheng.desktop.config.js", "site": "cross-env NODE_ENV=production cross-env DEMO_ENV=react bisheng build -c ./site/bisheng.desktop.config.js && cp CNAME _site", "deploy": "npm run clean && npm run site && bisheng gh-pages --push-only", "pub": "cross-env BABEL_ENV=dist antd-tools run pub", "rn-start": "node scripts/start.js", "ios": "concurrently \"npm run watch-tsc\" \"npm run _ios\"", "android": "concurrently \"npm run watch-tsc\" \"npm run _android\"", "_ios": "npx react-native run-ios --project-path rn-kitchen-sink/ios --scheme KitchenSink", "_android": "react-native run-android", "lint-staged": "lint-staged", "lint-staged:ts": "npm run tsc && eslint components/**/*.tsx", "lint-staged:es": "eslint ./.eslintrc.js", "tsc": "antd-tools run tsc"}, "lint-staged": {"components/**/*.tsx": ["npm run lint-staged:ts"], "{tests,site,scripts,components}/**/*.{js,jsx}": ["npm run lint-staged:es"]}, "typings": "lib/index.d.ts", "pre-commit": ["lint-staged"], "resolutions": {"graceful-fs": "^4.2.4"}}