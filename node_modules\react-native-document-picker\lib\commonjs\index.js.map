{"version": 3, "names": ["_reactNative", "require", "_invariant", "_interopRequireDefault", "_fileTypes", "_NativeDocumentPicker", "obj", "__esModule", "default", "types", "perPlatformTypes", "Platform", "OS", "exports", "pickDirectory", "params", "result", "pick", "mode", "allowMultiSelection", "type", "uri", "NativeDocumentPicker", "pickSingle", "opts", "options", "then", "results", "allFiles", "newOpts", "presentationStyle", "transitionStyle", "Array", "isArray", "doPick", "invariant", "every", "length", "includes", "TypeError", "copyTo", "releaseSecureAccess", "uris", "Promise", "resolve", "E_DOCUMENT_PICKER_CANCELED", "E_DOCUMENT_PICKER_IN_PROGRESS", "isCancel", "err", "isErrorWithCode", "isInProgress", "errorCode", "nativeModuleErrorInstance", "code", "_default"], "sourceRoot": "../../src", "sources": ["index.tsx"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,qBAAA,GAAAJ,OAAA;AAA6D,SAAAE,uBAAAG,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAWtD,MAAMG,KAAK,GAAGC,2BAAgB,CAACC,qBAAQ,CAACC,EAAE,CAAC;AAAAC,OAAA,CAAAJ,KAAA,GAAAA,KAAA;AAgB3C,eAAeK,aAAaA,CACjCC,MAA6E,EACpC;EACzC,IAAIJ,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzB,MAAMI,MAAM,GAAG,MAAMC,IAAI,CAAC;MACxB,GAAGF,MAAM;MACTG,IAAI,EAAE,MAAM;MACZC,mBAAmB,EAAE,KAAK;MAC1BC,IAAI,EAAE,CAAC,eAAe;IACxB,CAAC,CAAC;IACF,OAAO;MAAEC,GAAG,EAAEL,MAAM,CAAC,CAAC,CAAC,CAACK;IAAI,CAAC;EAC/B,CAAC,MAAM;IACL,OAAOC,0CAAoB,CAACR,aAAa,CAAC,CAAC;EAC7C;AACF;AAEO,SAASS,UAAUA,CAACC,IAA4B,EAAmC;EACxF,MAAMC,OAAO,GAAG;IACd,GAAGD,IAAI;IACPL,mBAAmB,EAAE;EACvB,CAAC;EACD,OAAOF,IAAI,CAACQ,OAAO,CAAC,CAACC,IAAI,CAAEC,OAAO,IAAKA,OAAO,CAAC,CAAC,CAAC,CAAC;AACpD;AAEO,SAASV,IAAIA,CAACO,IAA4B,EAAqC;EACpF,MAAMC,OAAO,GAAG;IACd;IACAN,mBAAmB,EAAE,KAAK;IAC1BC,IAAI,EAAE,CAACX,KAAK,CAACmB,QAAQ,CAAC;IACtB,GAAGJ;EACL,CAAC;EAED,MAAMK,OAAqB,GAAG;IAC5BC,iBAAiB,EAAE,WAAW;IAC9BC,eAAe,EAAE,eAAe;IAChC,GAAGN,OAAO;IACVL,IAAI,EAAEY,KAAK,CAACC,OAAO,CAACR,OAAO,CAACL,IAAI,CAAC,GAAGK,OAAO,CAACL,IAAI,GAAG,CAACK,OAAO,CAACL,IAAI;EAClE,CAAC;EAED,OAAOc,MAAM,CAACL,OAAO,CAAC;AACxB;AASA,SAASK,MAAMA,CAACT,OAAqB,EAAqC;EACxE,IAAAU,kBAAS,EACP,EAAE,UAAU,IAAIV,OAAO,CAAC,EACxB,qFACF,CAAC;EACD,IAAAU,kBAAS,EACP,EAAE,OAAO,IAAIV,OAAO,CAAC,EACrB,kFACF,CAAC;EAED,IAAAU,kBAAS,EACPV,OAAO,CAACL,IAAI,CAACgB,KAAK,CAAEhB,IAAa,IAAK,OAAOA,IAAI,KAAK,QAAQ,CAAC,EAC9D,6BAA4BK,OAAO,CAACL,IAAK,mEAC5C,CAAC;EACD,IAAAe,kBAAS,EACPV,OAAO,CAACL,IAAI,CAACiB,MAAM,GAAG,CAAC,EACvB,kHACF,CAAC;EAED,IAAAF,kBAAS,EACP,CAACV,OAAO,CAACL,IAAI,CAACkB,QAAQ,CAAC,QAAQ,CAAC,EAChC,wEACF,CAAC;EAED,IAAI,MAAM,IAAIb,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAACa,QAAQ,CAACb,OAAO,CAACP,IAAI,IAAI,EAAE,CAAC,EAAE;IACzE,MAAM,IAAIqB,SAAS,CAAC,uBAAuB,GAAGd,OAAO,CAACP,IAAI,CAAC;EAC7D;EAEA,IACE,QAAQ,IAAIO,OAAO,IACnB,CAAC,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAACa,QAAQ,CAACb,OAAO,CAACe,MAAM,IAAI,EAAE,CAAC,EACxE;IACA,MAAM,IAAID,SAAS,CAAC,yBAAyB,GAAGd,OAAO,CAACe,MAAM,CAAC;EACjE;EAEA,OAAOlB,0CAAoB,CAACL,IAAI,CAACQ,OAAO,CAAC;AAC3C;AAEO,SAASgB,mBAAmBA,CAACC,IAAmB,EAAiB;EACtE,IAAI/B,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzB,OAAO+B,OAAO,CAACC,OAAO,CAAC,CAAC;EAC1B;EAEA,IAAAT,kBAAS,EACPH,KAAK,CAACC,OAAO,CAACS,IAAI,CAAC,IAAIA,IAAI,CAACN,KAAK,CAAEf,GAAG,IAAK,OAAOA,GAAG,KAAK,QAAQ,CAAC,EAClE,6CAA4CqB,IAAK,EACpD,CAAC;EAED,OAAOpB,0CAAoB,CAACmB,mBAAmB,CAACC,IAAI,CAAC;AACvD;AAEA,MAAMG,0BAA0B,GAAG,0BAA0B;AAC7D,MAAMC,6BAA6B,GAAG,sBAAsB;AAIrD,SAASC,QAAQA,CAACC,GAAY,EAAW;EAC9C,OAAOC,eAAe,CAACD,GAAG,EAAEH,0BAA0B,CAAC;AACzD;AAEO,SAASK,YAAYA,CAACF,GAAY,EAAW;EAClD,OAAOC,eAAe,CAACD,GAAG,EAAEF,6BAA6B,CAAC;AAC5D;AAEA,SAASG,eAAeA,CAACD,GAAY,EAAEG,SAAiB,EAAW;EACjE,IAAIH,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,MAAM,IAAIA,GAAG,EAAE;IACnD,MAAMI,yBAAyB,GAAGJ,GAA6B;IAC/D,OAAO,CAAAI,yBAAyB,aAAzBA,yBAAyB,uBAAzBA,yBAAyB,CAAEC,IAAI,MAAKF,SAAS;EACtD;EACA,OAAO,KAAK;AACd;AAAC,IAAAG,QAAA,GAEc;EACbP,QAAQ;EACRG,YAAY;EACZT,mBAAmB;EACnB3B,aAAa;EACbG,IAAI;EACJM,UAAU;EACVd,KAAK;EACLC,gBAAgB,EAAhBA;AACF,CAAC;AAAAG,OAAA,CAAAL,OAAA,GAAA8C,QAAA"}