{"version": 3, "names": ["mimeTypes", "Object", "freeze", "allFiles", "audio", "csv", "doc", "docx", "images", "json", "pdf", "plainText", "ppt", "pptx", "video", "xls", "xlsx", "zip", "utis", "extensions", "perPlatformTypes", "android", "ios", "windows", "macos", "web", "exports", "mimesAndUtisAreEqual", "mimesAndExtensionsAreEqual", "typesAreEqual"], "sourceRoot": "../../src", "sources": ["fileTypes.ts"], "mappings": ";;;;;;AAAA,MAAMA,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC;EAC9BC,QAAQ,EAAE,KAAK;EACfC,KAAK,EAAE,SAAS;EAChBC,GAAG,EAAE,UAAU;EACfC,GAAG,EAAE,oBAAoB;EACzBC,IAAI,EAAE,yEAAyE;EAC/EC,MAAM,EAAE,SAAS;EACjBC,IAAI,EAAE,kBAAkB;EACxBC,GAAG,EAAE,iBAAiB;EACtBC,SAAS,EAAE,YAAY;EACvBC,GAAG,EAAE,+BAA+B;EACpCC,IAAI,EAAE,2EAA2E;EACjFC,KAAK,EAAE,SAAS;EAChBC,GAAG,EAAE,0BAA0B;EAC/BC,IAAI,EAAE,mEAAmE;EACzEC,GAAG,EAAE;AACP,CAAU,CAAC;AAEX,MAAMC,IAAI,GAAGjB,MAAM,CAACC,MAAM,CAAC;EACzBC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE,cAAc;EACrBC,GAAG,EAAE,oCAAoC;EACzCC,GAAG,EAAE,wBAAwB;EAC7BC,IAAI,EAAE,8CAA8C;EACpDC,MAAM,EAAE,cAAc;EACtBC,IAAI,EAAE,aAAa;EACnBC,GAAG,EAAE,eAAe;EACpBC,SAAS,EAAE,mBAAmB;EAC9BC,GAAG,EAAE,8BAA8B;EACnCC,IAAI,EAAE,gDAAgD;EACtDC,KAAK,EAAE,cAAc;EACrBC,GAAG,EAAE,yBAAyB;EAC9BC,IAAI,EAAE,wCAAwC;EAC9CC,GAAG,EAAE;AACP,CAAU,CAAC;AAEX,MAAME,UAAU,GAAGlB,MAAM,CAACC,MAAM,CAAC;EAC/BC,QAAQ,EAAE,GAAG;EACbC,KAAK,EACH,uHAAuH;EACzHC,GAAG,EAAE,MAAM;EACXC,GAAG,EAAE,MAAM;EACXC,IAAI,EAAE,OAAO;EACbC,MAAM,EAAE,iBAAiB;EACzBC,IAAI,EAAE,OAAO;EACbC,GAAG,EAAE,MAAM;EACXC,SAAS,EAAE,MAAM;EACjBC,GAAG,EAAE,MAAM;EACXC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,MAAM;EACbC,GAAG,EAAE,MAAM;EACXC,IAAI,EAAE,OAAO;EACbC,GAAG,EAAE;AACP,CAAU,CAAC;AAIJ,MAAMG,gBAAgB,GAAG;EAC9BC,OAAO,EAAErB,SAAS;EAClBsB,GAAG,EAAEJ,IAAI;EACTK,OAAO,EAAEJ,UAAU;EACnB;EACAK,KAAK,EAAEL,UAAU;EACjBM,GAAG,EAAEN;AACP,CAAC;;AAED;AACA;AAAAO,OAAA,CAAAN,gBAAA,GAAAA,gBAAA;AASA,MAAMO,oBAAoE,GAAG,IAAI;AACjF,MAAMC,0BAAgF,GAAG,IAAI;AACtF,MAAMC,aAAa,GAAGF,oBAAoB,IAAIC,0BAA0B;AAAAF,OAAA,CAAAG,aAAA,GAAAA,aAAA"}