{"name": "react-native-document-picker", "version": "9.3.1", "description": "A react native interface to access documents from dropbox, google drive, iCloud...", "main": "lib/commonjs/index", "module": "lib/module/index", "types": "lib/typescript/index.d.ts", "react-native": "src/index", "source": "src/index", "files": ["src", "lib", "android", "ios", "windows", "cpp", "react-native-document-picker.podspec", "!lib/typescript/example", "!android/build", "!ios/build", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__"], "scripts": {"test": "jest", "typescript": "tsc --noEmit", "lint": "eslint \"**/*.{js,ts,tsx}\"", "prepare": "bob build", "release": "yarn prepare && release-it", "pods": "cd example && pod-install --quiet", "bootstrap": "yarn && yarn pods", "start": "react-native start", "windows": "react-native run-windows --sln example/windows/document-picker-example.sln", "android": "react-native run-android", "ios": "react-native run-ios"}, "codegenConfig": {"name": "rndocumentpicker", "type": "modules", "jsSrcsDir": "src", "android": {"javaPackageName": "com.reactnativedocumentpicker"}}, "keywords": ["react-native", "document", "picker", "uidocumentmenuviewcontroller", "dropbox", "google-drive", "icloud"], "repository": "https://github.com/react-native-documents/document-picker", "author": "<PERSON>x0 <<EMAIL>> (https://github.com/rnmods), <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)", "license": "MIT", "bugs": {"url": "https://github.com/react-native-documents/document-picker/issues"}, "homepage": "https://github.com/react-native-documents/document-picker#readme", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@commitlint/config-conventional": "^13.2.0", "@react-native-community/eslint-config": "^3.1.0", "@release-it/conventional-changelog": "^3.3.0", "@types/invariant": "^2.2.35", "@types/jest": "^29.5.1", "@types/react": "^18.0.18", "commitlint": "^13.2.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-ft-flow": "^2.0.3", "eslint-plugin-prettier": "^4.2.1", "husky": "^4.2.5", "jest": "^29.0.2", "metro-react-native-babel-preset": "0.73.9", "pod-install": "^0.1.38", "prettier": "^2.7.1", "react": "18.2.0", "react-native": "0.71.8", "react-native-builder-bob": "^0.20.4", "react-native-test-app": "^2.5.3", "release-it": "^14.11.6", "typescript": "^4.8.2"}, "peerDependencies": {"react": "*", "react-native": "*", "react-native-windows": "*"}, "peerDependenciesMeta": {"react-native-windows": {"optional": true}}, "jest": {"preset": "react-native", "modulePathIgnorePatterns": ["<rootDir>/example/node_modules", "<rootDir>/lib/"]}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "yarn lint && yarn typescript"}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular"}}}, "eslintConfig": {"root": true, "extends": ["@react-native-community", "plugin:prettier/recommended"]}, "eslintIgnore": ["node_modules/", "lib/"], "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", ["typescript", {"project": "tsconfig.build.json"}]]}, "dependencies": {"invariant": "^2.2.4"}, "resolutions": {"@types/react": "^18.2.6"}}