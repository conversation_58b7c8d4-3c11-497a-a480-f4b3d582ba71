// PocketBase Helper Functions

import { ValidationErrors } from "../types/pocketbase"

/**
 * Handles PocketBase errors and converts them to user-friendly messages
 */
export const handlePocketBaseError = (error: any): string => {
  console.error("PocketBase Error:", error)

  // Network errors
  if (!error.response) {
    return "Network error. Please check your internet connection."
  }

  // Server errors
  if (error.status >= 500) {
    return "Server error. Please try again later."
  }

  // Client errors
  if (error.status === 400) {
    if (error.data?.data) {
      const firstError = Object.values(error.data.data)[0] as any
      if (firstError?.message) {
        return firstError.message
      }
    }
    return "Invalid request. Please check your input."
  }

  if (error.status === 401) {
    return "Authentication failed. Please check your credentials."
  }

  if (error.status === 403) {
    return "Access denied. You don't have permission to perform this action."
  }

  if (error.status === 404) {
    return "Resource not found."
  }

  if (error.status === 409) {
    return "This email is already registered. Please use a different email or sign in."
  }

  // Default error message
  return "An unexpected error occurred. Please try again."
}

/**
 * Extracts validation errors from PocketBase error response
 */
export const extractValidationErrors = (error: any): Partial<ValidationErrors> => {
  const errors: Partial<ValidationErrors> = {}

  if (error.data?.data) {
    const pbErrors = error.data.data

    if (pbErrors.email) {
      errors.email = pbErrors.email.message || "Invalid email address"
    }
    if (pbErrors.password) {
      errors.password = pbErrors.password.message || "Invalid password"
    }
    if (pbErrors.passwordConfirm) {
      errors.passwordConfirm = pbErrors.passwordConfirm.message || "Passwords do not match"
    }
    if (pbErrors.fullName) {
      errors.fullName = pbErrors.fullName.message || "Invalid full name"
    }
    if (pbErrors.mobileNumber) {
      errors.mobileNumber = pbErrors.mobileNumber.message || "Invalid mobile number"
    }
    if (pbErrors.companyName) {
      errors.companyName = pbErrors.companyName.message || "Invalid company name"
    }
  }

  return errors
}

/**
 * Validates email format
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Validates password strength
 */
export const validatePassword = (password: string): { isValid: boolean; message?: string } => {
  if (password.length < 8) {
    return { isValid: false, message: "Password must be at least 8 characters long" }
  }
  
  if (!/(?=.*[a-z])/.test(password)) {
    return { isValid: false, message: "Password must contain at least one lowercase letter" }
  }
  
  if (!/(?=.*[A-Z])/.test(password)) {
    return { isValid: false, message: "Password must contain at least one uppercase letter" }
  }
  
  if (!/(?=.*\d)/.test(password)) {
    return { isValid: false, message: "Password must contain at least one number" }
  }
  
  return { isValid: true }
}

/**
 * Validates mobile number format
 */
export const validateMobile = (mobile: string): boolean => {
  const cleanMobile = mobile.replace(/\D/g, '')
  return cleanMobile.length >= 10 && cleanMobile.length <= 15
}

/**
 * Formats mobile number for display
 */
export const formatMobileNumber = (mobile: string): string => {
  const cleanMobile = mobile.replace(/\D/g, '')
  
  if (cleanMobile.length === 10) {
    return `+91 ${cleanMobile.slice(0, 5)} ${cleanMobile.slice(5)}`
  }
  
  return mobile
}

/**
 * Checks if user has completed onboarding
 */
export const isOnboardingComplete = (user: any): boolean => {
  return !!(
    user?.emailVerified &&
    user?.onboardingCompleted &&
    user?.profileComplete &&
    user?.panCard &&
    user?.gstCertificate &&
    user?.addressProof
  )
}

/**
 * Gets user's onboarding progress percentage
 */
export const getOnboardingProgress = (user: any): number => {
  if (!user) return 0
  
  let progress = 0
  const steps = [
    user.id, // Account created
    user.emailVerified, // Email verified
    user.panCard, // PAN card uploaded
    user.gstCertificate, // GST certificate uploaded
    user.addressProof, // Address proof uploaded
    user.onboardingCompleted // Onboarding completed
  ]
  
  steps.forEach(step => {
    if (step) progress += 1
  })
  
  return Math.round((progress / steps.length) * 100)
}

/**
 * Determines the current onboarding step based on user data
 */
export const getCurrentOnboardingStep = (user: any): number => {
  if (!user?.id) return 1 // Account creation
  if (!user.emailVerified) return 2 // Email verification
  if (!user.panCard || !user.gstCertificate || !user.addressProof) return 3 // Document upload
  if (!user.onboardingCompleted) return 4 // Completion
  return 4 // Completed
}

/**
 * Formats file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Checks if file type is allowed for document upload
 */
export const isAllowedFileType = (mimeType: string): boolean => {
  const allowedTypes = [
    'application/pdf',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp'
  ]
  
  return allowedTypes.includes(mimeType.toLowerCase())
}

/**
 * Gets maximum file size for document upload (in bytes)
 */
export const getMaxFileSize = (): number => {
  return 5 * 1024 * 1024 // 5MB
}
