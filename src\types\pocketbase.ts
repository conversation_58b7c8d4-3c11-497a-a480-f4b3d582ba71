// PocketBase Types for Logistics Mobile App

export interface BaseModel {
  id: string
  created: string
  updated: string
}

export interface UserModel extends BaseModel {
  collectionId: string
  collectionName: string
  email: string
  name: string
  fullName: string
  mobileNumber: string
  companyName: string
  role: 'merchant' | 'admin' | 'user'
  emailVerified: boolean
  onboardingCompleted: boolean
  profileComplete: boolean
  emailVisibility: boolean
  panCard?: string
  gstCertificate?: string
  addressProof?: string
  expand?: { [key: string]: any }
}

export interface ServiceProviderModel extends BaseModel {
  name: string
  location: string
  rating: number
  reviews: number
  services: string[]
  description: string
  image?: string
  email: string
  phone: string
  website?: string
  areaSize?: string
  capacity?: string
  verified: boolean
  active: boolean
}

export interface OrderModel extends BaseModel {
  orderNumber: string
  userId: string
  serviceProviderId: string
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled'
  serviceType: string
  description: string
  estimatedCost?: number
  actualCost?: number
  scheduledDate?: string
  completedDate?: string
  notes?: string
  documents?: string[]
  expand?: {
    user?: UserModel
    serviceProvider?: ServiceProviderModel
  }
}

export interface ContainerModel extends BaseModel {
  containerNumber: string
  userId: string
  serviceProviderId?: string
  type: 'dry' | 'refrigerated' | 'open_top' | 'flat_rack' | 'tank'
  size: '20ft' | '40ft' | '45ft'
  status: 'available' | 'in_use' | 'maintenance' | 'retired'
  location: string
  lastInspection?: string
  nextInspection?: string
  documents?: string[]
  expand?: {
    user?: UserModel
    serviceProvider?: ServiceProviderModel
  }
}

export interface DocumentModel extends BaseModel {
  name: string
  type: 'panCard' | 'gstCertificate' | 'addressProof' | 'invoice' | 'contract' | 'other'
  userId: string
  orderId?: string
  containerId?: string
  file: string
  fileSize: number
  mimeType: string
  verified: boolean
  verifiedBy?: string
  verifiedAt?: string
  expand?: {
    user?: UserModel
    order?: OrderModel
    container?: ContainerModel
  }
}

export interface NotificationModel extends BaseModel {
  userId: string
  title: string
  message: string
  type: 'info' | 'warning' | 'error' | 'success'
  read: boolean
  actionUrl?: string
  expand?: {
    user?: UserModel
  }
}

// Form validation interfaces
export interface ValidationErrors {
  fullName: string
  mobileNumber: string
  companyName: string
  email: string
  password: string
  passwordConfirm: string
}

export interface DocumentStatus {
  panCard: boolean
  gstCertificate: boolean
  addressProof: boolean
}

// API Response types
export interface PocketBaseResponse<T> {
  page: number
  perPage: number
  totalItems: number
  totalPages: number
  items: T[]
}

export interface AuthResponse {
  token: string
  record: UserModel
}

// Collection names enum
export enum Collections {
  USERS = 'users',
  SERVICE_PROVIDERS = 'serviceProviders',
  ORDERS = 'orders',
  CONTAINERS = 'containers',
  DOCUMENTS = 'documents',
  NOTIFICATIONS = 'notifications'
}

// Service types enum
export enum ServiceTypes {
  PACKING = 'packing',
  STORAGE = 'storage',
  CUSTOMS = 'customs',
  PALLETIZING = 'palletizing',
  INSPECTION = 'inspection',
  TRANSPORTATION = 'transportation',
  WAREHOUSING = 'warehousing'
}

// Order status enum
export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// Container status enum
export enum ContainerStatus {
  AVAILABLE = 'available',
  IN_USE = 'in_use',
  MAINTENANCE = 'maintenance',
  RETIRED = 'retired'
}
